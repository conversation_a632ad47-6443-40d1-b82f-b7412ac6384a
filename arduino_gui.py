#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arduino Serial Controller GUI - Ultra Modern & Responsive
واجهة تحكم Arduino عبر السيريال - عصرية ومتجاوبة للغاية
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import serial
import serial.tools.list_ports
import threading
import time
import json
import os
import queue
from datetime import datetime
import math

class ModernArduinoController:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_styles()

        # Threading and communication
        self.command_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.serial_thread = None
        self.running = True

        # Serial connection
        self.serial_connection = None
        self.is_connected = False
        self.auto_refresh = False
        self.connection_lock = threading.Lock()

        # Data storage
        self.pwm_values = [0, 0, 0]
        self.stepper_data = {"angle": 0.0, "speed": 12, "mode": "IDLE"}
        self.relay_data = {
            "right": {"active": False, "timer": 5, "remaining": 0},
            "left": {"active": False, "timer": 5, "remaining": 0}
        }
        self.shoot_rate = 1
        self.continuous_shooting = False

        # Animation and visual effects
        self.animation_running = False
        self.pulse_animation_id = None
        self.connection_pulse_id = None

        # Theme and language settings
        self.current_theme = "dark"  # dark or light
        self.current_language = "en"  # en or ar
        self.themes = self.setup_themes()
        self.translations = self.setup_translations()

        # Settings file
        self.settings_file = "arduino_gui_settings.json"
        self.load_settings()

        # Apply theme first before creating widgets
        self.apply_theme()

        self.create_widgets()
        self.refresh_ports()

        # Start background threads
        self.start_serial_thread()
        self.start_response_handler()

        # Auto refresh timer
        self.auto_refresh_timer()

        # Responsive design
        self.setup_responsive_design()

        # Start visual effects
        self.start_visual_effects()

    def start_serial_thread(self):
        """Start background serial communication thread"""
        self.serial_thread = threading.Thread(target=self.serial_worker, daemon=True)
        self.serial_thread.start()

    def serial_worker(self):
        """Background thread for serial communication"""
        while self.running:
            try:
                # Check for commands to send
                if not self.command_queue.empty():
                    command, callback = self.command_queue.get(timeout=0.1)

                    with self.connection_lock:
                        if self.is_connected and self.serial_connection:
                            try:
                                # Send command
                                self.serial_connection.write((command + '\n').encode())
                                time.sleep(0.05)  # Small delay

                                # Read response
                                response = ""
                                start_time = time.time()
                                while time.time() - start_time < 1.0:  # 1 second timeout
                                    if self.serial_connection.in_waiting:
                                        response = self.serial_connection.readline().decode().strip()
                                        break
                                    time.sleep(0.01)

                                # Put response in queue
                                self.response_queue.put((command, response, callback))

                            except Exception as e:
                                self.response_queue.put((command, f"ERROR: {str(e)}", callback))
                        else:
                            self.response_queue.put((command, "ERROR: Not connected", callback))

                time.sleep(0.01)  # Small delay to prevent high CPU usage

            except queue.Empty:
                continue
            except Exception as e:
                print(f"Serial thread error: {e}")

    def start_response_handler(self):
        """Start response handler for GUI updates"""
        self.process_responses()

    def process_responses(self):
        """Process responses from serial thread"""
        try:
            while not self.response_queue.empty():
                command, response, callback = self.response_queue.get_nowait()

                # Log the response
                self.log_response(command, response)

                # Execute callback if provided
                if callback:
                    callback(response)

        except queue.Empty:
            pass
        except Exception as e:
            print(f"Response handler error: {e}")

        # Schedule next check
        self.root.after(50, self.process_responses)

    def start_visual_effects(self):
        """Start visual effects and animations"""
        self.animate_connection_indicator()

    def animate_connection_indicator(self):
        """Animate connection indicator with pulsing effect"""
        if hasattr(self, 'connection_indicator'):
            if self.is_connected:
                # Pulsing green effect
                self.connection_pulse_id = self.root.after(1000, self.pulse_connection_indicator)
            else:
                # Static red
                self.update_connection_indicator()

    def pulse_connection_indicator(self):
        """Create pulsing effect for connection indicator"""
        if hasattr(self, 'connection_indicator') and self.is_connected:
            # Create pulsing effect
            self.connection_indicator.delete("all")

            # Outer glow
            self.connection_indicator.create_oval(0, 0, 20, 20,
                                                fill=self.themes[self.current_theme]["success"],
                                                outline=self.themes[self.current_theme]["accent"],
                                                width=2)
            # Inner circle
            self.connection_indicator.create_oval(4, 4, 16, 16,
                                                fill=self.themes[self.current_theme]["accent"],
                                                outline="")

            # Schedule next pulse
            self.connection_pulse_id = self.root.after(1500, self.animate_connection_indicator)

    def t(self, key):
        """Get translated text for current language"""
        return self.translations[self.current_language].get(key, key)

    def setup_window(self):
        """Setup main window with modern design"""
        self.root.title("🚀 Arduino Controller Pro - تحكم Arduino المتقدم")

        # Get screen dimensions for responsive design
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Calculate window size based on screen size
        if screen_width <= 800:  # Mobile/small tablet
            width, height = int(screen_width * 0.95), int(screen_height * 0.9)
        elif screen_width <= 1366:  # Tablet/small laptop
            width, height = int(screen_width * 0.85), int(screen_height * 0.8)
        else:  # Desktop/large laptop
            width, height = min(1400, int(screen_width * 0.75)), min(900, int(screen_height * 0.8))

        # Center window on screen
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.root.geometry(f"{width}x{height}+{x}+{y}")
        self.root.minsize(800, 600)  # Minimum size

        # Modern window styling
        self.root.configure(bg='#1e1e1e')

        # Make window resizable and responsive
        self.root.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)

    def setup_styles(self):
        """Setup modern ttk styles"""
        self.style = ttk.Style()

        # Configure modern styles
        self.style.theme_use('clam')

    def setup_themes(self):
        """Setup ultra-modern dark and light themes with enhanced gradients"""
        return {
            "dark": {
                "bg": "#0f0f23",
                "fg": "#f8fafc",
                "select_bg": "#7c3aed",
                "select_fg": "#ffffff",
                "entry_bg": "#1e1b4b",
                "entry_fg": "#f1f5f9",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "button_active": "#6d28d9",
                "frame_bg": "#1e1b4b",
                "card_bg": "#312e81",
                "accent": "#06b6d4",
                "accent_hover": "#0891b2",
                "warning": "#f59e0b",
                "error": "#ef4444",
                "success": "#10b981",
                "info": "#3b82f6",
                "border": "#4c1d95",
                "border_light": "#6d28d9",
                "shadow": "#000000",
                "gradient_start": "#7c3aed",
                "gradient_end": "#06b6d4",
                "surface": "#262626",
                "surface_variant": "#374151"
            },
            "light": {
                "bg": "#f8fafc",
                "fg": "#0f172a",
                "select_bg": "#7c3aed",
                "select_fg": "#ffffff",
                "entry_bg": "#ffffff",
                "entry_fg": "#1e293b",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "button_active": "#6d28d9",
                "frame_bg": "#ffffff",
                "card_bg": "#f1f5f9",
                "accent": "#0ea5e9",
                "accent_hover": "#0284c7",
                "warning": "#f59e0b",
                "error": "#ef4444",
                "success": "#059669",
                "info": "#3b82f6",
                "border": "#e2e8f0",
                "border_light": "#cbd5e1",
                "shadow": "#64748b",
                "gradient_start": "#7c3aed",
                "gradient_end": "#0ea5e9",
                "surface": "#f1f5f9",
                "surface_variant": "#e2e8f0"
            }
        }

    def setup_translations(self):
        """Setup translations for English and Arabic"""
        return {
            "en": {
                "app_title": "Arduino Controller Pro",
                "app_subtitle": "Advanced Arduino Control",
                "serial_connection": "Serial Connection",
                "port": "Port",
                "speed": "Speed",
                "refresh": "Refresh",
                "connect": "Connect",
                "disconnect": "Disconnect",
                "connecting": "Connecting...",
                "connected": "Connected",
                "disconnected": "Disconnected",
                "pwm_control": "PWM Control",
                "red_channel": "Red Channel (D9)",
                "blue_channel": "Blue Channel (D6)",
                "green_channel": "Green Channel (D5)",
                "pulse_control": "Pulse Control",
                "single_pulse": "Single Pulse",
                "send_pulse": "Send Pulse",
                "continuous_pulses": "Continuous Pulses",
                "pulse_rate": "Pulse Rate",
                "start_pulses": "Start Pulses",
                "stop_pulses": "Stop Pulses",
                "status": "Status",
                "stopped": "Stopped",
                "active": "Active",
                "stepper_motor": "Stepper Motor",
                "motor_status": "Motor Status",
                "angle_control": "Angle Control",
                "target_angle": "Target Angle (0-359)",
                "goto_angle": "Go to Angle",
                "speed_rpm": "Speed (1-20 RPM)",
                "movement_control": "Movement Control",
                "clockwise": "Clockwise",
                "counter_clockwise": "Counter-Clockwise",
                "stop_motor": "Stop Motor",
                "reset_position": "Reset Position",
                "relay_control": "Relay Control",
                "right_relay": "Right Relay (D10)",
                "left_relay": "Left Relay (D11)",
                "timer_seconds": "Timer (seconds, 0 for manual)",
                "turn_on": "Turn On",
                "turn_off": "Turn Off",
                "settings": "Settings",
                "auto_refresh": "Auto Refresh",
                "auto_refresh_desc": "Auto refresh status every 2 seconds",
                "manual_refresh": "Manual Refresh",
                "save_settings": "Save Settings",
                "save_to_arduino": "Save Settings to Arduino",
                "reset_arduino": "Reset Arduino to Defaults",
                "event_log": "Event Log",
                "communication_log": "Communication Log",
                "clear_log": "Clear",
                "auto_scroll": "Auto Scroll",
                "dark_mode": "Dark",
                "light_mode": "Light",
                "language": "Language",
                "english": "English",
                "arabic": "العربية"
            },
            "ar": {
                "app_title": "تحكم Arduino المتقدم",
                "app_subtitle": "واجهة تحكم Arduino عصرية",
                "serial_connection": "اتصال السيريال",
                "port": "المنفذ",
                "speed": "السرعة",
                "refresh": "تحديث",
                "connect": "اتصال",
                "disconnect": "قطع الاتصال",
                "connecting": "جاري الاتصال...",
                "connected": "متصل",
                "disconnected": "غير متصل",
                "pwm_control": "تحكم PWM",
                "red_channel": "القناة الحمراء (D9)",
                "blue_channel": "القناة الزرقاء (D6)",
                "green_channel": "القناة الخضراء (D5)",
                "pulse_control": "تحكم النبضات",
                "single_pulse": "نبضة واحدة",
                "send_pulse": "إرسال نبضة",
                "continuous_pulses": "نبضات مستمرة",
                "pulse_rate": "معدل النبضات",
                "start_pulses": "بدء النبضات",
                "stop_pulses": "إيقاف النبضات",
                "status": "الحالة",
                "stopped": "متوقف",
                "active": "نشط",
                "stepper_motor": "المحرك المتدرج",
                "motor_status": "حالة المحرك",
                "angle_control": "التحكم بالزاوية",
                "target_angle": "الزاوية المطلوبة (0-359)",
                "goto_angle": "انتقال للزاوية",
                "speed_rpm": "السرعة (1-20 دورة/دقيقة)",
                "movement_control": "التحكم بالحركة",
                "clockwise": "مع عقارب الساعة",
                "counter_clockwise": "عكس عقارب الساعة",
                "stop_motor": "إيقاف المحرك",
                "reset_position": "إعادة تعيين الموضع",
                "relay_control": "تحكم المرحلات",
                "right_relay": "المرحل الأيمن (D10)",
                "left_relay": "المرحل الأيسر (D11)",
                "timer_seconds": "المؤقت (ثانية، 0 للتشغيل اليدوي)",
                "turn_on": "تشغيل",
                "turn_off": "إيقاف",
                "settings": "الإعدادات",
                "auto_refresh": "التحديث التلقائي",
                "auto_refresh_desc": "تحديث تلقائي للحالة كل ثانيتين",
                "manual_refresh": "تحديث يدوي",
                "save_settings": "حفظ الإعدادات",
                "save_to_arduino": "حفظ الإعدادات في Arduino",
                "reset_arduino": "إعادة تعيين Arduino للافتراضي",
                "event_log": "سجل الأحداث",
                "communication_log": "سجل الاتصالات",
                "clear_log": "مسح",
                "auto_scroll": "تمرير تلقائي",
                "dark_mode": "مظلم",
                "light_mode": "فاتح",
                "language": "اللغة",
                "english": "English",
                "arabic": "العربية"
            }
        }

    def apply_theme(self):
        """Apply current theme to ALL widgets with complete refresh"""
        theme = self.themes[self.current_theme]

        # Configure root window
        self.root.configure(bg=theme["bg"])

        # Configure ttk styles with enhanced theming
        try:
            self.style.configure('Modern.TFrame',
                               background=theme["card_bg"],
                               relief='flat',
                               borderwidth=1)

            self.style.configure('Modern.TLabelFrame',
                               background=theme["card_bg"],
                               foreground=theme["accent"],
                               relief='flat',
                               borderwidth=2,
                               labelmargins=(15, 8, 15, 8))

            self.style.configure('Modern.TLabel',
                               background=theme["card_bg"],
                               foreground=theme["fg"],
                               font=('Segoe UI', 11))

            self.style.configure('Title.TLabel',
                               background=theme["bg"],
                               foreground=theme["accent"],
                               font=('Segoe UI', 18, 'bold'))

            self.style.configure('Modern.TButton',
                               background=theme["button_bg"],
                               foreground=theme["button_fg"],
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(18, 10))

            self.style.map('Modern.TButton',
                          background=[('active', theme["button_hover"]),
                                    ('pressed', theme["button_active"])])

            self.style.configure('Accent.TButton',
                               background=theme["accent"],
                               foreground=theme["button_fg"],
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 11, 'bold'),
                               padding=(22, 12))

            self.style.map('Accent.TButton',
                          background=[('active', theme["accent_hover"]),
                                    ('pressed', theme["button_active"])])

            self.style.configure('Success.TButton',
                               background=theme["success"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(18, 10))

            self.style.configure('Warning.TButton',
                               background=theme["error"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(18, 10))

            self.style.configure('Modern.TCombobox',
                               fieldbackground=theme["entry_bg"],
                               background=theme["entry_bg"],
                               foreground=theme["entry_fg"],
                               borderwidth=1,
                               insertcolor=theme["fg"])

            self.style.configure('Modern.TEntry',
                               fieldbackground=theme["entry_bg"],
                               foreground=theme["entry_fg"],
                               borderwidth=1,
                               insertcolor=theme["fg"])

            self.style.configure('Modern.TNotebook',
                               background=theme["bg"],
                               borderwidth=0,
                               tabmargins=(5, 8, 5, 0))

            self.style.configure('Modern.TNotebook.Tab',
                               background=theme["surface"],
                               foreground=theme["fg"],
                               padding=(25, 15),
                               borderwidth=0,
                               font=('Segoe UI', 11, 'bold'))

            self.style.map('Modern.TNotebook.Tab',
                          background=[('selected', theme["accent"]),
                                    ('active', theme["accent_hover"])],
                          foreground=[('selected', '#ffffff'),
                                    ('active', '#ffffff')])
        except Exception as e:
            print(f"Warning: Could not apply some styles: {e}")

        # Force complete UI refresh after theme change
        if hasattr(self, 'main_container'):
            self.refresh_all_ui_elements()

    def setup_responsive_design(self):
        """Setup responsive design handlers"""
        self.root.bind('<Configure>', self.on_window_resize)

    def on_window_resize(self, event):
        """Handle window resize for responsive design"""
        if event.widget == self.root:
            width = event.width
            height = event.height

            # Adjust layout based on window size
            if hasattr(self, 'main_container'):
                if width < 900:  # Compact mode
                    self.switch_to_compact_mode()
                else:  # Normal mode
                    self.switch_to_normal_mode()

    def switch_to_compact_mode(self):
        """Switch to compact layout for small screens"""
        # This will be implemented in the widget creation
        pass

    def switch_to_normal_mode(self):
        """Switch to normal layout for larger screens"""
        # This will be implemented in the widget creation
        pass
        
    def create_widgets(self):
        """Create modern responsive widgets"""
        # Main container with modern styling
        self.main_container = ttk.Frame(self.root, padding="20")
        self.main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights for responsiveness
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_container.columnconfigure(0, weight=1)
        self.main_container.rowconfigure(2, weight=1)

        # Create header with title and theme toggle
        self.create_header()

        # Connection frame with modern design
        self.create_modern_connection_frame()

        # Control tabs with modern styling
        self.create_modern_control_tabs()

        # Status and log frame with modern design
        self.create_modern_status_frame()

    def create_header(self):
        """Create ultra-modern header with title and controls"""
        header_frame = ttk.Frame(self.main_container, style='Modern.TFrame')
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 30))
        header_frame.columnconfigure(1, weight=1)

        # App icon and title with modern styling
        title_frame = ttk.Frame(header_frame, style='Modern.TFrame')
        title_frame.grid(row=0, column=0, sticky=(tk.W))

        # Main title with gradient effect
        self.title_label = tk.Label(title_frame,
                                   text=f"🚀 {self.t('app_title')}",
                                   font=('Segoe UI', 20, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["bg"])
        self.title_label.pack(side=tk.LEFT)

        # Subtitle
        self.subtitle_label = tk.Label(title_frame,
                                      text=self.t('app_subtitle'),
                                      font=('Segoe UI', 11, 'italic'),
                                      fg=self.themes[self.current_theme]["fg"],
                                      bg=self.themes[self.current_theme]["bg"])
        self.subtitle_label.pack(side=tk.LEFT, padx=(15, 0))

        # Control buttons frame with modern styling
        controls_frame = ttk.Frame(header_frame, style='Modern.TFrame')
        controls_frame.grid(row=0, column=2, sticky=(tk.E))

        # Language toggle button
        self.language_btn = tk.Button(controls_frame,
                                     text=f"� {self.t('language')}",
                                     command=self.toggle_language,
                                     font=('Segoe UI', 9, 'bold'),
                                     bg=self.themes[self.current_theme]["button_bg"],
                                     fg=self.themes[self.current_theme]["button_fg"],
                                     activebackground=self.themes[self.current_theme]["button_hover"],
                                     activeforeground=self.themes[self.current_theme]["button_fg"],
                                     relief='flat',
                                     borderwidth=0,
                                     padx=15, pady=8,
                                     cursor='hand2')
        self.language_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # Theme toggle button with modern design
        theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
        self.theme_btn = tk.Button(controls_frame,
                                  text=theme_text,
                                  command=self.toggle_theme,
                                  font=('Segoe UI', 9, 'bold'),
                                  bg=self.themes[self.current_theme]["accent"],
                                  fg=self.themes[self.current_theme]["button_fg"],
                                  activebackground=self.themes[self.current_theme]["accent_hover"],
                                  activeforeground=self.themes[self.current_theme]["button_fg"],
                                  relief='flat',
                                  borderwidth=0,
                                  padx=15, pady=8,
                                  cursor='hand2')
        self.theme_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # Fullscreen button
        self.fullscreen_btn = tk.Button(controls_frame,
                                       text="⛶",
                                       command=self.toggle_fullscreen,
                                       font=('Segoe UI', 12, 'bold'),
                                       bg=self.themes[self.current_theme]["surface"],
                                       fg=self.themes[self.current_theme]["fg"],
                                       activebackground=self.themes[self.current_theme]["surface_variant"],
                                       activeforeground=self.themes[self.current_theme]["fg"],
                                       relief='flat',
                                       borderwidth=0,
                                       padx=12, pady=8,
                                       cursor='hand2')
        self.fullscreen_btn.pack(side=tk.RIGHT)

    def toggle_theme(self):
        """Toggle between dark and light themes with complete UI refresh"""
        if self.current_theme == "dark":
            self.current_theme = "light"
        else:
            self.current_theme = "dark"

        # Apply theme to all components
        self.apply_theme()
        self.refresh_all_ui_elements()

    def toggle_language(self):
        """Toggle between English and Arabic"""
        if self.current_language == "en":
            self.current_language = "ar"
        else:
            self.current_language = "en"

        # Refresh all UI text
        self.refresh_all_ui_elements()

    def refresh_all_ui_elements(self):
        """Refresh ALL UI elements with new theme and language - COMPLETE REFRESH"""
        theme = self.themes[self.current_theme]

        # Update main window
        self.root.configure(bg=theme["bg"])

        # Update header elements
        if hasattr(self, 'title_label'):
            self.title_label.config(
                text=f"🚀 {self.t('app_title')}",
                fg=theme["accent"],
                bg=theme["bg"]
            )

        if hasattr(self, 'subtitle_label'):
            self.subtitle_label.config(
                text=self.t('app_subtitle'),
                fg=theme["fg"],
                bg=theme["bg"]
            )

        # Update control buttons
        if hasattr(self, 'language_btn'):
            self.language_btn.config(
                text=f"🌐 {self.t('language')}",
                bg=theme["button_bg"],
                fg=theme["button_fg"],
                activebackground=theme["button_hover"]
            )

        if hasattr(self, 'theme_btn'):
            theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
            self.theme_btn.config(
                text=theme_text,
                bg=theme["accent"],
                fg=theme["button_fg"],
                activebackground=theme["accent_hover"]
            )

        if hasattr(self, 'fullscreen_btn'):
            self.fullscreen_btn.config(
                bg=theme["surface"],
                fg=theme["fg"],
                activebackground=theme["surface_variant"]
            )

        # Update connection frame elements
        if hasattr(self, 'status_label'):
            status_text = self.t('connected') if self.is_connected else self.t('disconnected')
            self.status_label.config(
                text=f"{'🟢' if self.is_connected else '🔴'} {status_text}",
                fg=theme["success"] if self.is_connected else theme["error"],
                bg=theme["bg"]
            )

        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.config(
                text=f"🔄 {self.t('refresh')}",
                bg=theme["surface"],
                fg=theme["fg"],
                activebackground=theme["surface_variant"]
            )

        if hasattr(self, 'connect_btn'):
            connect_text = self.t('disconnect') if self.is_connected else self.t('connect')
            self.connect_btn.config(
                text=f"{'�' if self.is_connected else '�'} {connect_text}",
                bg=theme["accent"],
                fg=theme["button_fg"],
                activebackground=theme["accent_hover"]
            )

        # Update connection indicator
        if hasattr(self, 'connection_indicator'):
            self.connection_indicator.config(bg=theme["bg"])
            self.update_connection_indicator()

        # Update all canvas backgrounds
        if hasattr(self, 'pwm_canvases'):
            for canvas in self.pwm_canvases:
                canvas.config(bg=theme["card_bg"])

        if hasattr(self, 'pulse_indicator'):
            self.pulse_indicator.config(bg=theme["card_bg"])

        # Update log text
        if hasattr(self, 'log_text'):
            self.log_text.config(
                bg=theme["entry_bg"],
                fg=theme["entry_fg"],
                insertbackground=theme["fg"],
                selectbackground=theme["select_bg"],
                selectforeground=theme["select_fg"]
            )

            # Update log text tags
            self.log_text.tag_configure("info", foreground=theme["info"])
            self.log_text.tag_configure("success", foreground=theme["success"])
            self.log_text.tag_configure("warning", foreground=theme["warning"])
            self.log_text.tag_configure("error", foreground=theme["error"])
            self.log_text.tag_configure("timestamp", foreground=theme["fg"])

        # Update notebook tabs
        if hasattr(self, 'notebook'):
            # Force notebook to refresh its styling
            self.notebook.configure(style='Modern.TNotebook')

        # Update PWM display
        if hasattr(self, 'pwm_values') and hasattr(self, 'pwm_canvases'):
            self.update_pwm_display()

        # Force a complete redraw
        self.root.update_idletasks()

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

        if current_state:
            self.fullscreen_btn.config(text="⛶")
        else:
            self.fullscreen_btn.config(text="🗗")
        
    def create_modern_connection_frame(self):
        """Create ultra-modern connection frame with responsive design"""
        # Create main connection card with modern styling
        conn_frame = tk.LabelFrame(self.main_container,
                                  text=f"🔌 {self.t('serial_connection')}",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.themes[self.current_theme]["accent"],
                                  bg=self.themes[self.current_theme]["bg"],
                                  labelanchor='nw',
                                  padx=25, pady=20,
                                  relief='flat',
                                  borderwidth=2,
                                  highlightbackground=self.themes[self.current_theme]["border"])
        conn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 25))
        conn_frame.columnconfigure(1, weight=1)
        conn_frame.columnconfigure(3, weight=1)

        # Connection status indicator with modern design
        status_frame = tk.Frame(conn_frame, bg=self.themes[self.current_theme]["bg"])
        status_frame.grid(row=0, column=0, columnspan=6, sticky=(tk.W, tk.E), pady=(0, 20))

        # Enhanced connection indicator
        self.connection_indicator = tk.Canvas(status_frame, width=30, height=30,
                                            highlightthickness=0,
                                            bg=self.themes[self.current_theme]["bg"])
        self.connection_indicator.pack(side=tk.LEFT, padx=(0, 15))

        # Status label with modern styling
        status_text = self.t('connected') if self.is_connected else self.t('disconnected')
        self.status_label = tk.Label(status_frame,
                                    text=f"{'🟢' if self.is_connected else '🔴'} {status_text}",
                                    font=('Segoe UI', 12, 'bold'),
                                    fg=self.themes[self.current_theme]["success"] if self.is_connected else self.themes[self.current_theme]["error"],
                                    bg=self.themes[self.current_theme]["bg"])
        self.status_label.pack(side=tk.LEFT)

        # Connection controls with modern card design
        controls_frame = tk.Frame(conn_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.grid(row=1, column=0, columnspan=6, sticky=(tk.W, tk.E))
        controls_frame.columnconfigure(1, weight=1)
        controls_frame.columnconfigure(3, weight=1)

        # Port selection with modern styling
        port_label = tk.Label(controls_frame,
                             text=f"📍 {self.t('port')}",
                             font=('Segoe UI', 11, 'bold'),
                             fg=self.themes[self.current_theme]["fg"],
                             bg=self.themes[self.current_theme]["bg"])
        port_label.grid(row=0, column=0, padx=(0, 15), sticky=tk.W)

        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(controls_frame, textvariable=self.port_var,
                                      font=('Segoe UI', 11), width=18,
                                      style='Modern.TCombobox')
        self.port_combo.grid(row=0, column=1, padx=(0, 25), sticky=(tk.W, tk.E))

        # Baud rate selection with modern styling
        speed_label = tk.Label(controls_frame,
                              text=f"⚡ {self.t('speed')}",
                              font=('Segoe UI', 11, 'bold'),
                              fg=self.themes[self.current_theme]["fg"],
                              bg=self.themes[self.current_theme]["bg"])
        speed_label.grid(row=0, column=2, padx=(0, 15), sticky=tk.W)

        self.baud_var = tk.StringVar(value="115200")
        baud_combo = ttk.Combobox(controls_frame, textvariable=self.baud_var,
                                 font=('Segoe UI', 11), width=12,
                                 values=["9600", "57600", "115200"],
                                 state="readonly",
                                 style='Modern.TCombobox')
        baud_combo.grid(row=0, column=3, padx=(0, 25), sticky=(tk.W, tk.E))

        # Action buttons with ultra-modern styling
        buttons_frame = tk.Frame(controls_frame, bg=self.themes[self.current_theme]["bg"])
        buttons_frame.grid(row=0, column=4, sticky=tk.E)

        # Refresh button
        self.refresh_btn = tk.Button(buttons_frame,
                                    text=f"🔄 {self.t('refresh')}",
                                    command=self.refresh_ports,
                                    font=('Segoe UI', 10, 'bold'),
                                    bg=self.themes[self.current_theme]["surface"],
                                    fg=self.themes[self.current_theme]["fg"],
                                    activebackground=self.themes[self.current_theme]["surface_variant"],
                                    activeforeground=self.themes[self.current_theme]["fg"],
                                    relief='flat',
                                    borderwidth=0,
                                    padx=18, pady=10,
                                    cursor='hand2')
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 12))

        # Connect button
        connect_text = self.t('disconnect') if self.is_connected else self.t('connect')
        self.connect_btn = tk.Button(buttons_frame,
                                    text=f"{'🔌' if self.is_connected else '🔗'} {connect_text}",
                                    command=self.toggle_connection,
                                    font=('Segoe UI', 10, 'bold'),
                                    bg=self.themes[self.current_theme]["accent"],
                                    fg=self.themes[self.current_theme]["button_fg"],
                                    activebackground=self.themes[self.current_theme]["accent_hover"],
                                    activeforeground=self.themes[self.current_theme]["button_fg"],
                                    relief='flat',
                                    borderwidth=0,
                                    padx=20, pady=10,
                                    cursor='hand2')
        self.connect_btn.pack(side=tk.LEFT)

        # Update connection indicator
        self.update_connection_indicator()

    def update_connection_indicator(self):
        """Update ultra-modern visual connection indicator"""
        self.connection_indicator.delete("all")

        if self.is_connected:
            # Connected - animated green with glow effect
            # Outer glow ring
            self.connection_indicator.create_oval(2, 2, 28, 28,
                                                fill=self.themes[self.current_theme]["success"],
                                                outline=self.themes[self.current_theme]["accent"],
                                                width=3)
            # Inner bright circle
            self.connection_indicator.create_oval(8, 8, 22, 22,
                                                fill=self.themes[self.current_theme]["accent"],
                                                outline="")
            # Center pulse dot
            self.connection_indicator.create_oval(12, 12, 18, 18,
                                                fill="#ffffff",
                                                outline="")

            # Update status text
            status_text = self.t('connected')
            self.status_label.config(
                text=f"🟢 {status_text}",
                fg=self.themes[self.current_theme]["success"]
            )
        else:
            # Disconnected - red indicator
            self.connection_indicator.create_oval(6, 6, 24, 24,
                                                fill=self.themes[self.current_theme]["error"],
                                                outline=self.themes[self.current_theme]["border"],
                                                width=2)

            # Update status text
            status_text = self.t('disconnected')
            self.status_label.config(
                text=f"🔴 {status_text}",
                fg=self.themes[self.current_theme]["error"]
            )

    def create_modern_control_tabs(self):
        """Create ultra-modern control tabs with complete theming"""
        # Ultra-modern notebook with enhanced styling
        self.notebook = ttk.Notebook(self.main_container, style='Modern.TNotebook')
        self.notebook.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 25))

        # Create tabs with ultra-modern design
        self.create_modern_pwm_tab()
        self.create_modern_shooting_tab()
        self.create_modern_stepper_tab()
        self.create_modern_relay_tab()
        self.create_modern_settings_tab()

        # Apply theme to notebook immediately
        self.notebook.configure(style='Modern.TNotebook')
        
    def create_modern_pwm_tab(self):
        """Create ultra-modern PWM control tab with enhanced visuals"""
        pwm_frame = tk.Frame(self.notebook, bg=self.themes[self.current_theme]["bg"], padx=30, pady=25)
        self.notebook.add(pwm_frame, text=f"🎨 {self.t('pwm_control')}")

        # Configure responsive grid
        pwm_frame.columnconfigure(0, weight=1)

        # PWM channels with ultra-modern design
        self.pwm_vars = []
        self.pwm_scales = []
        self.pwm_labels = []
        self.pwm_canvases = []

        channels = [
            (self.t('red_channel'), "#ef4444", "🔴"),
            (self.t('blue_channel'), "#3b82f6", "🔵"),
            (self.t('green_channel'), "#10b981", "🟢")
        ]

        for i, (name, color_main, _) in enumerate(channels):
            # Ultra-modern card with gradient background
            card_frame = tk.LabelFrame(pwm_frame,
                                      text=f"  {name}  ",
                                      font=('Segoe UI', 12, 'bold'),
                                      fg=self.themes[self.current_theme]["accent"],
                                      bg=self.themes[self.current_theme]["card_bg"],
                                      labelanchor='nw',
                                      padx=25, pady=20,
                                      relief='flat',
                                      borderwidth=2,
                                      highlightbackground=self.themes[self.current_theme]["border_light"])
            card_frame.grid(row=i, column=0, sticky=(tk.W, tk.E), padx=0, pady=15)
            card_frame.columnconfigure(1, weight=1)

            # Enhanced visual indicator with ultra-modern design
            indicator_frame = tk.Frame(card_frame, bg=self.themes[self.current_theme]["card_bg"])
            indicator_frame.grid(row=0, column=0, padx=(0, 25), sticky=tk.W)

            # Create ultra-modern circular color preview with glow
            color_canvas = tk.Canvas(indicator_frame, width=100, height=100,
                                   highlightthickness=0,
                                   bg=self.themes[self.current_theme]["card_bg"])
            color_canvas.pack()

            # Create enhanced gradient circle effect
            self.create_gradient_circle(color_canvas, color_main, self.pwm_values[i])
            self.pwm_canvases.append(color_canvas)

            # Control section
            control_frame = ttk.Frame(card_frame)
            control_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))
            control_frame.columnconfigure(0, weight=1)

            # Value variable
            var = tk.IntVar(value=self.pwm_values[i])
            self.pwm_vars.append(var)

            # Modern slider with custom styling
            slider_frame = ttk.Frame(control_frame)
            slider_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
            slider_frame.columnconfigure(0, weight=1)

            scale = tk.Scale(slider_frame, from_=0, to=255, orient=tk.HORIZONTAL,
                           variable=var, command=lambda val, ch=i: self.on_pwm_change(ch, val),
                           bg=self.themes[self.current_theme]["frame_bg"],
                           fg=self.themes[self.current_theme]["fg"],
                           activebackground=color_main,
                           highlightthickness=0,
                           troughcolor=self.themes[self.current_theme]["entry_bg"],
                           font=('Segoe UI', 9, 'bold'),
                           length=300, width=20)
            scale.grid(row=0, column=0, sticky=(tk.W, tk.E))
            self.pwm_scales.append(scale)

            # Value display section
            value_frame = ttk.Frame(control_frame)
            value_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

            # Digital value display
            value_label = ttk.Label(value_frame, text="0 (0.00V)",
                                   font=('Segoe UI', 12, 'bold'))
            value_label.pack(side=tk.LEFT)
            self.pwm_labels.append(value_label)

            # Quick preset buttons with modern styling
            presets_frame = ttk.Frame(card_frame)
            presets_frame.grid(row=0, column=2, sticky=tk.E)

            preset_values = [("OFF", 0), ("25%", 64), ("50%", 128), ("75%", 192), ("MAX", 255)]

            for j, (label, value) in enumerate(preset_values):
                btn = ttk.Button(presets_frame, text=label,
                               command=lambda ch=i, val=value: self.set_pwm_value(ch, val),
                               width=6)
                btn.grid(row=j, column=0, pady=2, sticky=(tk.W, tk.E))

        # Update PWM display
        self.update_pwm_display()

    def create_gradient_circle(self, canvas, color, intensity):
        """Create a modern gradient circle for PWM visualization"""
        canvas.delete("all")

        # Calculate color intensity
        intensity = max(0, min(1, intensity / 255.0))

        # Parse color
        if color.startswith('#'):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
        else:
            r, g, b = 128, 128, 128  # Default gray

        # Apply intensity
        r = int(r * intensity)
        g = int(g * intensity)
        b = int(b * intensity)

        # Create gradient effect with multiple circles
        center_x, center_y = 40, 40
        max_radius = 35

        for i in range(max_radius, 0, -2):
            # Calculate alpha for gradient effect
            alpha = (max_radius - i) / max_radius

            # Adjust color for gradient
            grad_r = int(r + (255 - r) * alpha * 0.3)
            grad_g = int(g + (255 - g) * alpha * 0.3)
            grad_b = int(b + (255 - b) * alpha * 0.3)

            grad_color = f"#{grad_r:02x}{grad_g:02x}{grad_b:02x}"

            # Draw circle
            x1, y1 = center_x - i, center_y - i
            x2, y2 = center_x + i, center_y + i
            canvas.create_oval(x1, y1, x2, y2, fill=grad_color, outline="")

        # Add intensity text
        if intensity > 0.1:
            text_color = "white" if intensity > 0.5 else "black"
            canvas.create_text(center_x, center_y, text=f"{int(intensity * 255)}",
                             fill=text_color, font=('Segoe UI', 12, 'bold'))

        # Add glow effect for high intensity
        if intensity > 0.7:
            glow_color = f"#{min(255, r + 50):02x}{min(255, g + 50):02x}{min(255, b + 50):02x}"
            canvas.create_oval(center_x - max_radius - 5, center_y - max_radius - 5,
                             center_x + max_radius + 5, center_y + max_radius + 5,
                             outline=glow_color, width=2)

    def create_modern_shooting_tab(self):
        """Create modern shooting control tab"""
        shoot_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(shoot_frame, text="⚡ تحكم النبضات")

        shoot_frame.columnconfigure(0, weight=1)

        # Single shot section with modern design
        single_card = ttk.LabelFrame(shoot_frame, text="⚡ نبضة واحدة - Single Pulse",
                                    padding="25")
        single_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Large pulse button with visual feedback
        pulse_frame = ttk.Frame(single_card)
        pulse_frame.pack(expand=True)

        self.pulse_btn = ttk.Button(pulse_frame, text="🔥 إرسال نبضة\nSend Pulse",
                                   command=self.single_shoot)
        self.pulse_btn.pack(pady=10)

        # Enhanced pulse indicator with modern design
        self.pulse_indicator = tk.Canvas(pulse_frame, width=60, height=60,
                                       highlightthickness=0, bg=self.themes[self.current_theme]["frame_bg"])
        self.pulse_indicator.pack(pady=(10, 0))

        # Initialize pulse indicator
        self.create_pulse_indicator(False)

        # Continuous shooting section
        cont_card = ttk.LabelFrame(shoot_frame, text="🔄 نبضات مستمرة - Continuous Pulses",
                                  padding="25")
        cont_card.grid(row=1, column=0, sticky=(tk.W, tk.E))
        cont_card.columnconfigure(0, weight=1)

        # Rate control with modern slider
        rate_section = ttk.Frame(cont_card)
        rate_section.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        rate_section.columnconfigure(1, weight=1)

        ttk.Label(rate_section, text="📊 معدل النبضات",
                 font=('Segoe UI', 11, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 15))

        self.rate_var = tk.IntVar(value=self.shoot_rate)
        rate_scale = tk.Scale(rate_section, from_=0, to=10, orient=tk.HORIZONTAL,
                             variable=self.rate_var, command=self.on_rate_change,
                             bg=self.themes[self.current_theme]["frame_bg"],
                             fg=self.themes[self.current_theme]["fg"],
                             activebackground=self.themes[self.current_theme]["accent"],
                             highlightthickness=0,
                             troughcolor=self.themes[self.current_theme]["entry_bg"],
                             font=('Segoe UI', 10, 'bold'),
                             length=250, width=25)
        rate_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))

        self.rate_label = ttk.Label(rate_section, text=f"{self.shoot_rate} Hz",
                                   font=('Segoe UI', 12, 'bold'))
        self.rate_label.grid(row=0, column=2, sticky=tk.E)

        # Control buttons with modern styling
        controls_section = ttk.Frame(cont_card)
        controls_section.grid(row=1, column=0, pady=(0, 15))

        self.start_shoot_btn = ttk.Button(controls_section, text="▶️ بدء النبضات",
                                         command=self.start_continuous_shooting)
        self.start_shoot_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_shoot_btn = ttk.Button(controls_section, text="⏹️ إيقاف النبضات",
                                        command=self.stop_continuous_shooting)
        self.stop_shoot_btn.pack(side=tk.LEFT)

        # Status display with visual indicator
        status_section = ttk.Frame(cont_card)
        status_section.grid(row=2, column=0, sticky=(tk.W, tk.E))

        self.shoot_status_indicator = tk.Canvas(status_section, width=20, height=20,
                                              highlightthickness=0, bg=self.themes[self.current_theme]["frame_bg"])
        self.shoot_status_indicator.pack(side=tk.LEFT, padx=(0, 10))

        self.shoot_status_label = ttk.Label(status_section, text="⏸️ الحالة: متوقف",
                                           font=('Segoe UI', 11, 'bold'))
        self.shoot_status_label.pack(side=tk.LEFT)

        # Update shooting status indicator
        self.update_shooting_indicator()

    def update_shooting_indicator(self):
        """Create modern stepper motor control tab"""
        stepper_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(stepper_frame, text="🔄 المحرك المتدرج")

        stepper_frame.columnconfigure(0, weight=1)
        stepper_frame.columnconfigure(1, weight=1)

        # Status display
        status_card = ttk.LabelFrame(stepper_frame, text="📊 حالة المحرك - Motor Status", padding="20")
        status_card.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        self.stepper_status_label = ttk.Label(status_card, text="الزاوية: 0.0° | السرعة: 12 RPM | الوضع: IDLE",
                                             font=('Segoe UI', 11, 'bold'))
        self.stepper_status_label.pack()

        # Angle control
        angle_card = ttk.LabelFrame(stepper_frame, text="🎯 التحكم بالزاوية - Angle Control", padding="20")
        angle_card.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10), pady=(0, 10))

        # Angle input
        angle_input_frame = ttk.Frame(angle_card)
        angle_input_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(angle_input_frame, text="الزاوية المطلوبة (0-359):", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.angle_var = tk.IntVar(value=0)
        angle_entry = ttk.Entry(angle_input_frame, textvariable=self.angle_var, width=15, font=('Segoe UI', 12))
        angle_entry.pack(pady=(5, 10))

        ttk.Button(angle_input_frame, text="🎯 انتقال للزاوية", command=self.goto_angle).pack()

        # Speed control
        speed_frame = ttk.Frame(angle_card)
        speed_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(speed_frame, text="السرعة (1-20 RPM):", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.speed_var = tk.IntVar(value=self.stepper_data["speed"])
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, orient=tk.HORIZONTAL,
                              variable=self.speed_var, command=self.on_speed_change,
                              font=('Segoe UI', 10), length=200)
        speed_scale.pack(pady=(5, 0))

        self.speed_label = ttk.Label(speed_frame, text=f"{self.stepper_data['speed']} RPM",
                                    font=('Segoe UI', 11, 'bold'))
        self.speed_label.pack()

        # Movement control
        move_card = ttk.LabelFrame(stepper_frame, text="🎮 التحكم بالحركة - Movement Control", padding="20")
        move_card.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0), pady=(0, 10))

        ttk.Button(move_card, text="↻ دوران مع عقارب الساعة", command=self.stepper_cw).pack(fill=tk.X, pady=5)
        ttk.Button(move_card, text="↺ دوران عكس عقارب الساعة", command=self.stepper_ccw).pack(fill=tk.X, pady=5)
        ttk.Button(move_card, text="⏹️ إيقاف الحركة", command=self.stepper_stop).pack(fill=tk.X, pady=5)
        ttk.Button(move_card, text="🔄 إعادة تعيين الموضع", command=self.stepper_reset).pack(fill=tk.X, pady=5)

    def create_modern_relay_tab(self):
        """Create modern relay control tab"""
        relay_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(relay_frame, text="🔌 تحكم المرحلات")

        relay_frame.columnconfigure(0, weight=1)
        relay_frame.columnconfigure(1, weight=1)

        # Right relay
        self.create_modern_relay_control(relay_frame, "RIGHT", "🔌 المرحل الأيمن (D10)", 0, 0)

        # Left relay
        self.create_modern_relay_control(relay_frame, "LEFT", "🔌 المرحل الأيسر (D11)", 0, 1)

    def create_modern_relay_control(self, parent, relay_id, title, row, col):
        """Create modern relay control card"""
        card = ttk.LabelFrame(parent, text=title, padding="20")
        card.grid(row=row, column=col, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # Timer setting
        timer_frame = ttk.Frame(card)
        timer_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(timer_frame, text="المؤقت (ثانية، 0 للتشغيل اليدوي):",
                 font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        timer_var = tk.IntVar(value=self.relay_data[relay_id.lower()]["timer"])
        setattr(self, f"timer_{relay_id.lower()}_var", timer_var)

        timer_entry = ttk.Entry(timer_frame, textvariable=timer_var, width=15, font=('Segoe UI', 12))
        timer_entry.pack(pady=(5, 0))

        # Control buttons
        btn_frame = ttk.Frame(card)
        btn_frame.pack(fill=tk.X, pady=(0, 15))

        on_btn = ttk.Button(btn_frame, text=f"🟢 تشغيل",
                           command=lambda: self.relay_on(relay_id))
        on_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

        off_btn = ttk.Button(btn_frame, text=f"🔴 إيقاف",
                            command=lambda: self.relay_off(relay_id))
        off_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Status display
        status_label = ttk.Label(card, text="الحالة: متوقف", font=('Segoe UI', 11, 'bold'))
        status_label.pack()
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)

    def create_modern_settings_tab(self):
        """Create modern settings tab"""
        settings_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(settings_frame, text="⚙️ الإعدادات")

        settings_frame.columnconfigure(0, weight=1)

        # Auto refresh
        auto_card = ttk.LabelFrame(settings_frame, text="🔄 التحديث التلقائي", padding="20")
        auto_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        ttk.Checkbutton(auto_card, text="تحديث تلقائي للحالة كل ثانيتين",
                       variable=self.auto_refresh_var, command=self.toggle_auto_refresh).pack(anchor=tk.W)

        ttk.Button(auto_card, text="🔄 تحديث الحالة يدوياً",
                  command=self.manual_refresh).pack(pady=(15, 0))

        # Save/Load settings
        save_card = ttk.LabelFrame(settings_frame, text="💾 حفظ الإعدادات", padding="20")
        save_card.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(save_card, text="💾 حفظ الإعدادات في Arduino",
                  command=self.save_arduino_settings).pack(fill=tk.X, pady=5)
        ttk.Button(save_card, text="🔄 إعادة تعيين Arduino للإعدادات الافتراضية",
                  command=self.reset_arduino_settings).pack(fill=tk.X, pady=5)
        """Update shooting status visual indicator"""
        self.shoot_status_indicator.delete("all")
        if self.continuous_shooting:
            # Animated green circle for active shooting
            self.shoot_status_indicator.create_oval(2, 2, 18, 18,
                                                   fill=self.themes[self.current_theme]["success"],
                                                   outline="")
            self.shoot_status_label.config(text=f"🟢 نشط: {self.shoot_rate} Hz")
        else:
            # Gray circle for stopped
            self.shoot_status_indicator.create_oval(2, 2, 18, 18,
                                                   fill=self.themes[self.current_theme]["entry_bg"],
                                                   outline="")
            self.shoot_status_label.config(text="⏸️ الحالة: متوقف")

    def create_pulse_indicator(self, active=False):
        """Create modern pulse indicator"""
        self.pulse_indicator.delete("all")

        if active:
            # Active pulse with glow effect
            center_x, center_y = 30, 30

            # Outer glow
            self.pulse_indicator.create_oval(5, 5, 55, 55,
                                           fill=self.themes[self.current_theme]["accent"],
                                           outline=self.themes[self.current_theme]["accent"],
                                           width=3)

            # Inner bright circle
            self.pulse_indicator.create_oval(15, 15, 45, 45,
                                           fill=self.themes[self.current_theme]["success"],
                                           outline="")

            # Center dot
            self.pulse_indicator.create_oval(25, 25, 35, 35,
                                           fill="#ffffff",
                                           outline="")
        else:
            # Inactive state
            self.pulse_indicator.create_oval(20, 20, 40, 40,
                                           fill=self.themes[self.current_theme]["entry_bg"],
                                           outline=self.themes[self.current_theme]["border"],
                                           width=2)

    def flash_pulse_indicator(self):
        """Enhanced flash pulse indicator with animation"""
        def animate_pulse(step=0):
            if step < 6:  # 6 animation steps
                self.create_pulse_indicator(step % 2 == 0)  # Alternate between active/inactive
                self.pulse_animation_id = self.root.after(100, lambda: animate_pulse(step + 1))
            else:
                self.create_pulse_indicator(False)  # Return to inactive state

        animate_pulse()

    def create_modern_stepper_tab(self):
        stepper_frame = ttk.Frame(self.notebook)
        self.notebook.add(stepper_frame, text="المحرك المتدرج")
        
        # Status display
        status_frame = ttk.LabelFrame(stepper_frame, text="حالة المحرك - Motor Status", padding="10")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        self.stepper_status_label = ttk.Label(status_frame, text="الزاوية: 0.0° | السرعة: 12 RPM | الوضع: IDLE")
        self.stepper_status_label.pack()
        
        # Angle control
        angle_frame = ttk.LabelFrame(stepper_frame, text="التحكم بالزاوية - Angle Control", padding="10")
        angle_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        angle_input_frame = ttk.Frame(angle_frame)
        angle_input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(angle_input_frame, text="الزاوية المطلوبة (0-359):").pack(side=tk.LEFT)
        self.angle_var = tk.IntVar(value=0)
        angle_entry = ttk.Entry(angle_input_frame, textvariable=self.angle_var, width=10)
        angle_entry.pack(side=tk.LEFT, padx=(10, 10))
        
        ttk.Button(angle_input_frame, text="انتقال للزاوية", 
                  command=self.goto_angle).pack(side=tk.LEFT)
        
        # Speed control
        speed_frame = ttk.Frame(angle_frame)
        speed_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(speed_frame, text="السرعة (1-20 RPM):").pack(side=tk.LEFT)
        self.speed_var = tk.IntVar(value=self.stepper_data["speed"])
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, orient=tk.HORIZONTAL,
                              variable=self.speed_var, command=self.on_speed_change)
        speed_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        self.speed_label = ttk.Label(speed_frame, text=f"{self.stepper_data['speed']} RPM")
        self.speed_label.pack(side=tk.LEFT)
        
        # Movement control
        move_frame = ttk.LabelFrame(stepper_frame, text="التحكم بالحركة - Movement Control", padding="10")
        move_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(move_frame, text="دوران مع عقارب الساعة", 
                  command=self.stepper_cw).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="دوران عكس عقارب الساعة", 
                  command=self.stepper_ccw).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="إيقاف الحركة", 
                  command=self.stepper_stop).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="إعادة تعيين الموضع", 
                  command=self.stepper_reset).pack(fill=tk.X, pady=2)
        
    def create_relay_tab(self):
        relay_frame = ttk.Frame(self.notebook)
        self.notebook.add(relay_frame, text="تحكم المرحلات")
        
        # Right relay
        self.create_relay_control(relay_frame, "RIGHT", "المرحل الأيمن (D10)", 0)
        
        # Left relay  
        self.create_relay_control(relay_frame, "LEFT", "المرحل الأيسر (D11)", 1)
        
    def create_relay_control(self, parent, relay_id, title, row):
        frame = ttk.LabelFrame(parent, text=title, padding="10")
        frame.grid(row=row, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        frame.columnconfigure(1, weight=1)
        
        # Timer setting
        timer_frame = ttk.Frame(frame)
        timer_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(timer_frame, text="المؤقت (ثانية، 0 للتشغيل اليدوي):").pack(side=tk.LEFT)
        
        timer_var = tk.IntVar(value=self.relay_data[relay_id.lower()]["timer"])
        setattr(self, f"timer_{relay_id.lower()}_var", timer_var)
        
        timer_entry = ttk.Entry(timer_frame, textvariable=timer_var, width=10)
        timer_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Control buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        on_btn = ttk.Button(btn_frame, text=f"تشغيل {title.split()[0]}", 
                           command=lambda: self.relay_on(relay_id))
        on_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        off_btn = ttk.Button(btn_frame, text=f"إيقاف {title.split()[0]}", 
                            command=lambda: self.relay_off(relay_id))
        off_btn.pack(side=tk.LEFT)
        
        # Status display
        status_label = ttk.Label(frame, text="الحالة: متوقف")
        status_label.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)
        
    def create_settings_tab(self):
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="الإعدادات")
        
        # Auto refresh
        auto_frame = ttk.LabelFrame(settings_frame, text="التحديث التلقائي", padding="10")
        auto_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        ttk.Checkbutton(auto_frame, text="تحديث تلقائي للحالة كل ثانيتين", 
                       variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack()
        
        # Manual refresh
        ttk.Button(auto_frame, text="تحديث الحالة يدوياً", 
                  command=self.manual_refresh).pack(pady=(10, 0))
        
        # Save/Load settings
        save_frame = ttk.LabelFrame(settings_frame, text="حفظ الإعدادات", padding="10")
        save_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(save_frame, text="حفظ الإعدادات في Arduino", 
                  command=self.save_arduino_settings).pack(pady=2)
        ttk.Button(save_frame, text="إعادة تعيين Arduino للإعدادات الافتراضية", 
                  command=self.reset_arduino_settings).pack(pady=2)
        
    def create_modern_status_frame(self):
        """Create ultra-modern status and log frame with complete theming"""
        # Ultra-modern status frame with enhanced styling
        status_frame = tk.LabelFrame(self.main_container,
                                    text=f"📊 {self.t('event_log')}",
                                    font=('Segoe UI', 12, 'bold'),
                                    fg=self.themes[self.current_theme]["accent"],
                                    bg=self.themes[self.current_theme]["bg"],
                                    labelanchor='nw',
                                    padx=25, pady=20,
                                    relief='flat',
                                    borderwidth=2,
                                    highlightbackground=self.themes[self.current_theme]["border"])
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)

        # Log controls frame with modern styling
        controls_frame = tk.Frame(status_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        controls_frame.columnconfigure(0, weight=1)

        # Log title and controls
        title_frame = tk.Frame(controls_frame, bg=self.themes[self.current_theme]["bg"])
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        title_frame.columnconfigure(0, weight=1)

        # Title label
        title_label = tk.Label(title_frame,
                              text=f"📝 {self.t('communication_log')}",
                              font=('Segoe UI', 11, 'bold'),
                              fg=self.themes[self.current_theme]["fg"],
                              bg=self.themes[self.current_theme]["bg"])
        title_label.pack(side=tk.LEFT)

        # Log control buttons with modern styling
        log_controls = tk.Frame(title_frame, bg=self.themes[self.current_theme]["bg"])
        log_controls.pack(side=tk.RIGHT)

        # Clear button
        clear_btn = tk.Button(log_controls,
                             text=f"🗑️ {self.t('clear_log')}",
                             command=self.clear_log,
                             font=('Segoe UI', 9, 'bold'),
                             bg=self.themes[self.current_theme]["surface"],
                             fg=self.themes[self.current_theme]["fg"],
                             activebackground=self.themes[self.current_theme]["surface_variant"],
                             activeforeground=self.themes[self.current_theme]["fg"],
                             relief='flat',
                             borderwidth=0,
                             padx=15, pady=6,
                             cursor='hand2')
        clear_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Auto scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_check = tk.Checkbutton(log_controls,
                                          text=self.t('auto_scroll'),
                                          variable=self.auto_scroll_var,
                                          font=('Segoe UI', 9),
                                          fg=self.themes[self.current_theme]["fg"],
                                          bg=self.themes[self.current_theme]["bg"],
                                          activeforeground=self.themes[self.current_theme]["fg"],
                                          activebackground=self.themes[self.current_theme]["bg"],
                                          selectcolor=self.themes[self.current_theme]["accent"])
        auto_scroll_check.pack(side=tk.RIGHT, padx=(5, 0))

        # Ultra-modern log text area
        log_container = tk.Frame(status_frame, bg=self.themes[self.current_theme]["bg"])
        log_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_container.columnconfigure(0, weight=1)
        log_container.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(
            log_container,
            height=8,
            width=80,
            bg=self.themes[self.current_theme]["entry_bg"],
            fg=self.themes[self.current_theme]["entry_fg"],
            insertbackground=self.themes[self.current_theme]["fg"],
            selectbackground=self.themes[self.current_theme]["select_bg"],
            selectforeground=self.themes[self.current_theme]["select_fg"],
            font=('Consolas', 10),
            wrap=tk.WORD,
            relief='flat',
            borderwidth=2,
            highlightthickness=1,
            highlightbackground=self.themes[self.current_theme]["border"],
            highlightcolor=self.themes[self.current_theme]["accent"]
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure enhanced text tags for colored logging
        self.log_text.tag_configure("info", foreground=self.themes[self.current_theme]["info"])
        self.log_text.tag_configure("success", foreground=self.themes[self.current_theme]["success"])
        self.log_text.tag_configure("warning", foreground=self.themes[self.current_theme]["warning"])
        self.log_text.tag_configure("error", foreground=self.themes[self.current_theme]["error"])
        self.log_text.tag_configure("timestamp", foreground=self.themes[self.current_theme]["fg"],
                                   font=('Consolas', 9))

        # Welcome message
        welcome_msg = "🚀 Ultra-Modern Arduino Controller Started" if self.current_language == "en" else "🚀 تم تشغيل واجهة التحكم العصرية"
        self.log(welcome_msg, "success")
        
    def log(self, message, level="info"):
        """Add message to log with timestamp and color coding"""
        timestamp = time.strftime("%H:%M:%S")

        # Insert timestamp
        self.log_text.insert(tk.END, f"[{timestamp}] ", "timestamp")

        # Insert message with appropriate color
        self.log_text.insert(tk.END, f"{message}\n", level)

        # Auto-scroll if enabled
        if hasattr(self, 'auto_scroll_var') and self.auto_scroll_var.get():
            self.log_text.see(tk.END)

    def clear_log(self):
        """Clear the log"""
        self.log_text.delete(1.0, tk.END)
        self.log("📝 تم مسح السجل", "info")

    def refresh_ports(self):
        """Refresh available serial ports"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports and not self.port_var.get():
            self.port_var.set(ports[0])

    def toggle_connection(self):
        """Toggle serial connection"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """Connect to Arduino with modern feedback using threading"""
        def connect_thread():
            try:
                port = self.port_var.get()
                baud = int(self.baud_var.get())

                if not port:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", "يرجى اختيار منفذ"))
                    return

                self.root.after(0, lambda: self.log(f"🔄 محاولة الاتصال بـ {port} بسرعة {baud}...", "info"))

                # Disable connect button during connection
                self.root.after(0, lambda: self.connect_btn.config(state='disabled', text="🔄 جاري الاتصال..."))

                with self.connection_lock:
                    self.serial_connection = serial.Serial(port, baud, timeout=1)
                    time.sleep(2)  # Wait for Arduino to initialize

                    self.is_connected = True

                # Update UI in main thread
                self.root.after(0, self.on_connection_success)

            except Exception as e:
                # Update UI in main thread
                self.root.after(0, lambda: self.on_connection_error(str(e)))

        # Start connection in background thread
        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connection_success(self):
        """Handle successful connection in main thread"""
        self.connect_btn.config(state='normal', text="🔌 قطع الاتصال")
        self.update_connection_indicator()
        self.log(f"✅ تم الاتصال بنجاح بـ {self.port_var.get()}", "success")

        # Get initial status
        self.manual_refresh()

    def on_connection_error(self, error_msg):
        """Handle connection error in main thread"""
        self.connect_btn.config(state='normal', text="🔗 اتصال")
        messagebox.showerror("خطأ في الاتصال", f"فشل الاتصال: {error_msg}")
        self.log(f"❌ فشل الاتصال: {error_msg}", "error")

    def disconnect(self):
        """Disconnect from Arduino with modern feedback"""
        with self.connection_lock:
            if self.serial_connection:
                self.serial_connection.close()
                self.serial_connection = None

            self.is_connected = False

        self.connect_btn.config(text="🔗 اتصال")
        self.update_connection_indicator()
        self.log("🔌 تم قطع الاتصال", "warning")

    def send_command(self, command, callback=None):
        """Send command to Arduino using threading (non-blocking)"""
        if not self.is_connected:
            self.log(f"⚠️ خطأ: غير متصل - {command}", "warning")
            return None

        # Add command to queue for background thread
        self.command_queue.put((command, callback))
        return True

    def send_command_sync(self, command):
        """Send command synchronously (blocking) - use sparingly"""
        if not self.is_connected or not self.serial_connection:
            self.log(f"⚠️ خطأ: غير متصل - {command}", "warning")
            return None

        try:
            with self.connection_lock:
                self.serial_connection.write((command + '\n').encode())
                time.sleep(0.05)  # Small delay for Arduino to process

                response = ""
                start_time = time.time()
                while time.time() - start_time < 1:  # 1 second timeout
                    if self.serial_connection.in_waiting:
                        response = self.serial_connection.readline().decode().strip()
                        break
                    time.sleep(0.01)

                # Log the response
                self.log_response(command, response)
                return response

        except Exception as e:
            self.log(f"❌ خطأ في الإرسال: {str(e)}", "error")
            return None

    def log_response(self, command, response):
        """Log command response with color coding"""
        # Color-coded logging based on response
        if response:
            if "ERROR" in response:
                self.log(f"📤 {command} → ❌ {response}", "error")
            elif "OK" in response or "COMPLETE" in response or "STARTED" in response or "STOPPED" in response:
                self.log(f"📤 {command} → ✅ {response}", "success")
            else:
                self.log(f"📤 {command} → 📥 {response}", "info")
        else:
            self.log(f"📤 {command} → ⏰ لا توجد استجابة", "warning")

    def on_pwm_change(self, channel, value):
        """Handle PWM value change with visual feedback"""
        value = int(float(value))
        self.pwm_values[channel] = value

        # Update display
        voltage = (value / 255.0) * 5.0
        self.pwm_labels[channel].config(text=f"{value} ({voltage:.2f}V)")

        # Update color preview with intensity
        self.update_pwm_color_preview(channel, value)

        # Send to Arduino
        self.send_command(f"SET_PWM,{channel},{value}")

    def update_pwm_color_preview(self, channel, value):
        """Update color preview canvas with modern gradient effect"""
        if hasattr(self, 'pwm_canvases') and channel < len(self.pwm_canvases):
            canvas = self.pwm_canvases[channel]

            # Base colors for each channel (modern palette)
            base_colors = [
                "#ef4444",  # Modern red
                "#3b82f6",  # Modern blue
                "#10b981"   # Modern green
            ]

            color = base_colors[channel]

            # Create gradient circle with intensity
            self.create_gradient_circle(canvas, color, value)

    def set_pwm_value(self, channel, value):
        """Set PWM value directly"""
        self.pwm_vars[channel].set(value)
        self.on_pwm_change(channel, value)

    def update_pwm_display(self):
        """Update PWM display values with modern visual effects"""
        for i in range(3):
            value = self.pwm_values[i]
            voltage = (value / 255.0) * 5.0
            percentage = (value / 255.0) * 100

            # Enhanced display with percentage
            self.pwm_labels[i].config(text=f"{value} ({voltage:.2f}V) - {percentage:.0f}%")

            # Update gradient circle
            if hasattr(self, 'pwm_canvases'):
                self.update_pwm_color_preview(i, value)

    def single_shoot(self):
        """Send single pulse with visual feedback"""
        self.send_command("SINGLE_SHOOT")
        # Flash visual indicator
        self.flash_pulse_indicator()

    def on_rate_change(self, value):
        """Handle shooting rate change"""
        self.shoot_rate = int(float(value))
        self.rate_label.config(text=f"{self.shoot_rate} Hz")
        self.send_command(f"SET_RATE,{self.shoot_rate}")
        # Update status if shooting is active
        if self.continuous_shooting:
            self.update_shooting_indicator()

    def start_continuous_shooting(self):
        """Start continuous shooting with visual feedback"""
        response = self.send_command("START_SHOOT")
        if response and "STARTED" in response:
            self.continuous_shooting = True
            self.update_shooting_indicator()
            # Disable start button, enable stop button
            self.start_shoot_btn.config(state='disabled')
            self.stop_shoot_btn.config(state='normal')

    def stop_continuous_shooting(self):
        """Stop continuous shooting with visual feedback"""
        response = self.send_command("STOP_SHOOT")
        if response and "STOPPED" in response:
            self.continuous_shooting = False
            self.update_shooting_indicator()
            # Enable start button, disable stop button
            self.start_shoot_btn.config(state='normal')
            self.stop_shoot_btn.config(state='disabled')

    def goto_angle(self):
        """Move stepper to specific angle"""
        angle = self.angle_var.get()
        if 0 <= angle < 360:
            self.send_command(f"STEPPER_ANGLE,{angle}")
        else:
            messagebox.showerror("خطأ", "الزاوية يجب أن تكون بين 0 و 359")

    def on_speed_change(self, value):
        """Handle stepper speed change"""
        speed = int(float(value))
        self.stepper_data["speed"] = speed
        self.speed_label.config(text=f"{speed} RPM")
        self.send_command(f"STEPPER_SPEED,{speed}")

    def stepper_cw(self):
        """Start clockwise rotation"""
        self.send_command("STEPPER_CW")

    def stepper_ccw(self):
        """Start counter-clockwise rotation"""
        self.send_command("STEPPER_CCW")

    def stepper_stop(self):
        """Stop stepper motor"""
        self.send_command("STEPPER_STOP")

    def stepper_reset(self):
        """Reset stepper position"""
        self.send_command("STEPPER_RESET")

    def relay_on(self, relay_id):
        """Turn relay on"""
        timer_var = getattr(self, f"timer_{relay_id.lower()}_var")
        timer = timer_var.get()
        self.send_command(f"RELAY_{relay_id}_ON,{timer}")

    def relay_off(self, relay_id):
        """Turn relay off"""
        self.send_command(f"RELAY_{relay_id}_OFF")

    def manual_refresh(self):
        """Manually refresh all status"""
        if not self.is_connected:
            return

        # Get PWM values
        response = self.send_command("GET_PWM")
        if response and "PWM_VALUES:" in response:
            values = response.split(":")[1].split(",")
            for i, val in enumerate(values[:3]):
                self.pwm_values[i] = int(val)
                self.pwm_vars[i].set(int(val))
            self.update_pwm_display()

        # Get stepper status
        response = self.send_command("GET_STEPPER")
        if response and "STEPPER_STATUS:" in response:
            data = response.split(":")[1].split(",")
            if len(data) >= 3:
                self.stepper_data["angle"] = float(data[0])
                self.stepper_data["speed"] = int(data[1])
                self.stepper_data["mode"] = data[2]
                self.stepper_status_label.config(
                    text=f"الزاوية: {self.stepper_data['angle']:.1f}° | "
                         f"السرعة: {self.stepper_data['speed']} RPM | "
                         f"الوضع: {self.stepper_data['mode']}")

        # Get relay status
        response = self.send_command("GET_RELAY")
        if response and "RELAY_STATUS:" in response:
            data = response.split(":")[1].split(",")
            if len(data) >= 6:
                # Right relay
                self.relay_data["right"]["active"] = data[0] == "1"
                self.relay_data["right"]["timer"] = int(data[1])
                self.relay_data["right"]["remaining"] = int(data[2])

                # Left relay
                self.relay_data["left"]["active"] = data[3] == "1"
                self.relay_data["left"]["timer"] = int(data[4])
                self.relay_data["left"]["remaining"] = int(data[5])

                # Update display
                self.update_relay_display()

    def update_relay_display(self):
        """Update relay status display"""
        for relay_id in ["right", "left"]:
            data = self.relay_data[relay_id]
            status_label = getattr(self, f"relay_{relay_id}_status")

            if data["active"]:
                if data["remaining"] > 0:
                    status_text = f"الحالة: نشط (متبقي: {data['remaining']/1000:.1f}s)"
                else:
                    status_text = "الحالة: نشط (يدوي)"
            else:
                status_text = "الحالة: متوقف"

            status_label.config(text=status_text)

    def toggle_auto_refresh(self):
        """Toggle auto refresh"""
        self.auto_refresh = self.auto_refresh_var.get()

    def auto_refresh_timer(self):
        """Auto refresh timer"""
        if self.auto_refresh and self.is_connected:
            self.manual_refresh()
        self.root.after(2000, self.auto_refresh_timer)  # Every 2 seconds

    def save_arduino_settings(self):
        """Save settings to Arduino EEPROM"""
        response = self.send_command("SAVE")
        if response and "SAVED" in response:
            messagebox.showinfo("نجح", "تم حفظ الإعدادات في Arduino")
        else:
            messagebox.showerror("خطأ", "فشل في حفظ الإعدادات")

    def reset_arduino_settings(self):
        """Reset Arduino to default settings"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            response = self.send_command("RESET")
            if response and "COMPLETE" in response:
                messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات")
                self.manual_refresh()
            else:
                messagebox.showerror("خطأ", "فشل في إعادة التعيين")

    def load_settings(self):
        """Load GUI settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.auto_refresh = settings.get('auto_refresh', False)
                    self.current_theme = settings.get('theme', 'dark')
                    self.current_language = settings.get('language', 'en')
        except Exception:
            self.auto_refresh = False
            self.current_theme = 'dark'
            self.current_language = 'en'

    def save_settings(self):
        """Save GUI settings to file"""
        try:
            settings = {
                'auto_refresh': self.auto_refresh,
                'theme': self.current_theme,
                'language': self.current_language
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception:
            pass

    def on_closing(self):
        """Handle window closing with proper cleanup"""
        # Stop all threads and animations
        self.running = False

        # Cancel any pending animations
        if self.pulse_animation_id:
            self.root.after_cancel(self.pulse_animation_id)
        if self.connection_pulse_id:
            self.root.after_cancel(self.connection_pulse_id)

        # Save settings
        self.save_settings()

        # Disconnect if connected
        if self.is_connected:
            self.disconnect()

        # Wait a moment for threads to finish
        time.sleep(0.1)

        self.root.destroy()

def main():
    root = tk.Tk()
    app = ModernArduinoController(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
